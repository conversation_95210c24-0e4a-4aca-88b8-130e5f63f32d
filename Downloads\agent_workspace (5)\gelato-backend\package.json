{"name": "gelato-backend", "version": "1.0.0", "description": "義式手工冰淇淋電商系統後端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:prod": "npm run init-db && npm start", "dev": "nodemon src/app.js", "init-db": "node src/utils/initDatabase.js", "build": "echo 'No build step required for Node.js'", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-session": "^1.18.1", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "multer": "^1.4.4", "node-telegram-bot-api": "^0.61.0", "sqlite3": "^5.1.6", "svg-captcha": "^1.4.0", "uuid": "^9.0.0", "validator": "^13.9.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["nodejs", "express", "sqlite", "ecommerce", "gelato", "ice-cream"], "author": "Your Name", "license": "MIT"}